<template>
  <view class="page-ranking">
    <!-- Header -->
    <view class="header">
      <view class="tab-switch">
        <view :class="['tab-item', currentTab === 'hot' ? 'tab-active' : '']" @tap="switchTab('hot')">
          <text class="tab-text">热剧榜</text>
        </view>
        <view :class="['tab-item', currentTab === 'weird' ? 'tab-active' : '']" @tap="switchTab('weird')">
          <text class="tab-text">猎奇榜</text>
        </view>
      </view>
      <!-- <SearchInput v-model="searchValue" placeholder="搜索剧名、演员、题材" @confirm="onSearchConfirm" /> -->
    </view>

    <!-- Main Content -->
    <scroll-view ref="scrollViewRef" class="main-content" scroll-y :scroll-top="scrollTop"
      @scrolltolower="onScrollToLower" lower-threshold="100">

      <!-- Ranking List -->
      <view class="ranking-list">
        <view v-for="(item, index) in currentRankingList" :key="item.id" class="ranking-item" @tap="onDramaClick(item)">
          <view class="drama-cover">
            <view class="cover-placeholder">
              <text class="cover-text">封面</text>
            </view>
            <view :class="['ranking-badge', getRankingBadgeClass(index + 1)]">
              <text class="ranking-number">{{ index + 1 }}</text>
            </view>
          </view>
          <view class="drama-info">
            <text class="drama-title">{{ item.title }}</text>
            <view class="drama-tags">
              <text v-for="tag in item.tags" :key="tag" class="drama-tag">
                {{ tag }}
              </text>
            </view>
            <view class="drama-reason">
              <text class="reason-label">推荐理由:</text>
              <text class="reason-text">{{ item.reason }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import './index.scss'
import { ref, computed, onMounted } from 'vue'
import SearchInput from '../../components/SearchInput.vue'
import Taro from '@tarojs/taro'
import { api } from '../../utils/api'

const searchValue = ref('')
const currentTab = ref('hot') // 默认显示热剧榜
const isLoading = ref(false) // 加载状态
const loading = ref(false)
const scrollTop = ref(0) // 滚动位置控制

// 排行榜数据
const hotRankingData = ref([])
const weirdRankingData = ref([])

// 加载排行榜数据
const loadRankingData = async (type = 'hot') => {
  try {
    loading.value = true
    const response = await api.getTagRanking({ type, page: 1, limit: 10 })
    if (response.code === 200) {
      const data = response.data

      // 更新排行榜数据
      if (type === 'hot') {
        hotRankingData.value = data.ranking || []
        hotTagSections.value = data.tags || []
      } else {
        weirdRankingData.value = data.ranking || []
        weirdTagSections.value = data.tags || []
      }
    }
  } catch (error) {
    console.error('加载排行榜数据失败:', error)
    // 显示错误提示
    if (typeof Taro !== 'undefined' && Taro.showToast) {
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadRankingData('hot')
  loadRankingData('weird')
})


// 热剧榜标签区块数据
const hotTagSections = ref([
  {
    id: 1,
    name: '霸道总裁',
    dramas: [
      { id: 1, title: '霸道总裁爱上我', heat: '100万人正在看', mainTag: '霸总' },
      { id: 2, title: '总裁的替身新娘', heat: '85万人正在看', mainTag: '霸总' },
      { id: 3, title: '闪婚霸道总裁', heat: '72万人正在看', mainTag: '霸总' },
      { id: 4, title: '总裁的秘密情人', heat: '68万人正在看', mainTag: '霸总' }
    ]
  },
  {
    id: 2,
    name: '重生逆袭',
    dramas: [
      { id: 5, title: '重生之商业帝国', heat: '95万人正在看', mainTag: '重生' },
      { id: 6, title: '重生豪门千金', heat: '78万人正在看', mainTag: '重生' },
      { id: 7, title: '重生之复仇女王', heat: '65万人正在看', mainTag: '重生' },
      { id: 8, title: '重生娱乐圈', heat: '58万人正在看', mainTag: '重生' }
    ]
  },
  {
    id: 3,
    name: '古装宫斗',
    dramas: [
      { id: 9, title: '医妃倾天下', heat: '88万人正在看', mainTag: '古装' },
      { id: 10, title: '皇后的复仇', heat: '76万人正在看', mainTag: '古装' },
      { id: 11, title: '宫心计', heat: '63万人正在看', mainTag: '古装' },
      { id: 12, title: '凤凰于飞', heat: '55万人正在看', mainTag: '古装' }
    ]
  }
])

// 猎奇榜标签区块数据
const weirdTagSections = ref([
  {
    id: 1,
    name: '惊悚悬疑',
    dramas: [
      { id: 1, title: '我在精神病院学斩神', heat: '120万人正在看', mainTag: '悬疑' },
      { id: 2, title: '阴阳保姆', heat: '95万人正在看', mainTag: '悬疑' },
      { id: 3, title: '诡异事件簿', heat: '82万人正在看', mainTag: '悬疑' },
      { id: 4, title: '午夜凶铃', heat: '75万人正在看', mainTag: '悬疑' }
    ]
  },
  {
    id: 2,
    name: '乡村伦理',
    dramas: [
      { id: 5, title: '寡妇村的秘密', heat: '110万人正在看', mainTag: '伦理' },
      { id: 6, title: '山村往事', heat: '88万人正在看', mainTag: '伦理' },
      { id: 7, title: '禁忌之恋', heat: '76万人正在看', mainTag: '伦理' },
      { id: 8, title: '乡村爱情', heat: '68万人正在看', mainTag: '伦理' }
    ]
  },
  {
    id: 3,
    name: '狗血剧情',
    dramas: [
      { id: 9, title: '我当上门女婿那些年', heat: '105万人正在看', mainTag: '狗血' },
      { id: 10, title: '七个姐姐逼我继承亿万家产', heat: '92万人正在看', mainTag: '狗血' },
      { id: 11, title: '豪门弃子的逆袭', heat: '79万人正在看', mainTag: '狗血' },
      { id: 12, title: '废物女婿变神豪', heat: '71万人正在看', mainTag: '狗血' }
    ]
  }
])



// 当前显示的榜单数据
const currentRankingList = computed(() => {
  return currentTab.value === 'hot' ? hotRankingData.value : weirdRankingData.value
})

// 当前显示的标签区块数据
const tagSections = computed(() => {
  return currentTab.value === 'hot' ? hotTagSections.value : weirdTagSections.value
})

// 切换标签
function switchTab(tab) {
  if (currentTab.value !== tab) {
    currentTab.value = tab

    // 滚动到页面顶部 - 使用多种方式确保兼容性
    scrollTop.value = 0

    // 使用Taro的pageScrollTo作为备用方案
    if (typeof Taro !== 'undefined' && Taro.pageScrollTo) {
      Taro.pageScrollTo({
        scrollTop: 0,
        duration: 300
      })
    }

    // 如果当前tab的数据为空，则重新加载
    if (tab === 'hot' && hotRankingData.value.length === 0) {
      loadRankingData('hot')
    } else if (tab === 'weird' && weirdRankingData.value.length === 0) {
      loadRankingData('weird')
    }
  }
}

// 获取排名徽章样式
function getRankingBadgeClass(rank) {
  if (rank === 1) return 'badge-first'
  if (rank === 2) return 'badge-second'
  if (rank === 3) return 'badge-third'
  return 'badge-other'
}

// 跳转到短剧详情页
function onDramaClick(drama) {
  console.log('跳转到短剧详情页:', drama)
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/drama/index?title=${encodeURIComponent(drama.title)}`
    })
  }
}

// 搜索确认跳转
function onSearchConfirm(searchText) {
  if (searchText && searchText.trim()) {
    console.log('搜索跳转:', searchText)
    if (typeof Taro !== 'undefined' && Taro.navigateTo) {
      Taro.navigateTo({
        url: `/pages/search/index?q=${encodeURIComponent(searchText.trim())}`
      })
    }
  }
}

// 获取标签样式类
function getTagClass(tag) {
  const tagClassMap = {
    '霸总': 'tag-red',
    '重生': 'tag-green',
    '古装': 'tag-purple',
    '悬疑': 'tag-blue',
    '甜宠': 'tag-yellow',
    '伦理': 'tag-orange',
    '狗血': 'tag-pink',
    '灵异': 'tag-dark',
    '重口': 'tag-black',
    '反转': 'tag-cyan'
  }
  return tagClassMap[tag] || 'tag-gray'
}

// 标签区块更多点击
function onTagSectionMoreClick(section) {
  console.log('标签页面暂时不可用:', section.name)
  if (typeof Taro !== 'undefined' && Taro.showToast) {
    Taro.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  }
}

// 滚动到底部加载更多
async function onScrollToLower() {
  if (isLoading.value) return

  console.log('滚动到底部，加载更多标签区块')
  isLoading.value = true

  try {
    // 计算当前页数
    const currentSections = currentTab.value === 'hot' ? hotTagSections.value : weirdTagSections.value
    const nextPage = Math.floor(currentSections.length / 3) + 1

    const response = await api.getTagRanking({
      type: currentTab.value,
      page: nextPage,
      limit: 3
    })

    if (response.code === 200 && response.data.tags) {
      const newSections = response.data.tags
      if (newSections.length > 0) {
        if (currentTab.value === 'hot') {
          hotTagSections.value.push(...newSections)
        } else {
          weirdTagSections.value.push(...newSections)
        }
      }
    }
  } catch (error) {
    console.error('加载更多数据失败:', error)
    if (typeof Taro !== 'undefined' && Taro.showToast) {
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  } finally {
    isLoading.value = false
  }
}
</script>
